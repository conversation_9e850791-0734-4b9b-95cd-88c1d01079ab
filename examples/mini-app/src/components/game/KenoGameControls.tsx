import { <PERSON><PERSON><PERSON><PERSON> } from "@betswirl/sdk-core"
import React from "react"
import { <PERSON><PERSON> } from "../ui/button"
import { GameMultiplierDisplay } from "./shared/GameMultiplierDisplay"
import { GameControlsProps } from "./shared/types"

interface KenoGameControlsProps extends GameControlsProps {
  selectedNumbers: KenoBall[]
  onNumbersChange: (numbers: KenoBall[]) => void
}

interface NumberButtonProps {
  number: KenoBall
  isSelected: boolean
  isDisabled: boolean
  onClick: (number: KenoBall) => void
}

interface MultiplierItemProps {
  value: string
  isVisible: boolean
}

const BUTTON_STYLES = {
  unselected: {
    background: "bg-[oklch(0.9846_0.0017_247.84)]",
    border: "border border-[oklch(0.9278_0.0029_264.54)] border-inset",
    text: "text-black",
    hover: "hover:brightness-105",
  },
  selected: {
    background: "bg-primary",
    border: "",
    text: "text-primary-foreground",
    hover: "hover:brightness-105",
  },
  disabled: "disabled:opacity-[0.72]",
  common: "w-[40px] h-[40px] p-0 text-[12px] font-medium rounded-md shadow-none transition-all duration-150",
} as const

const MULTIPLIER_VALUES = [
  "480.48x",
  "9.61x", 
  "1.07x",
  "0.40x",
  "0.46x",
  "1.91x",
  "1.02x",
  "0.87x",
]

const NumberButton = React.memo<NumberButtonProps>(
  ({ number, isSelected, isDisabled, onClick }) => {
    const styles = isSelected ? BUTTON_STYLES.selected : BUTTON_STYLES.unselected
    const buttonClasses = `${BUTTON_STYLES.common} ${styles.background} ${styles.border} ${styles.text} ${styles.hover} ${BUTTON_STYLES.disabled}`

    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onClick(number)}
        disabled={isDisabled && !isSelected}
        className={buttonClasses}
      >
        {number}
      </Button>
    )
  },
)

const MultiplierItem = React.memo<MultiplierItemProps>(({ value, isVisible }) => {
  if (!isVisible) return null

  return (
    <div className="w-[48px] h-[15px] flex items-center justify-center text-[10px] leading-[15px] text-[oklch(0.1559_0.02_269.69)] opacity-40 font-medium">
      {value}
    </div>
  )
})

export function KenoGameControls({
  selectedNumbers,
  onNumbersChange,
  multiplier,
  isDisabled,
}: KenoGameControlsProps) {
  const isNumberSelected = (number: KenoBall) => selectedNumbers.includes(number)

  const handleNumberClick = (number: KenoBall) => {
    if (isDisabled) return

    if (isNumberSelected(number)) {
      onNumbersChange(selectedNumbers.filter((n) => n !== number))
    } else {
      onNumbersChange([...selectedNumbers, number])
    }
  }

  const visibleMultipliersCount = selectedNumbers.length + 1
  const numbers: KenoBall[] = Array.from({ length: 40 }, (_, i) => (i + 1) as KenoBall)

  const renderNumberGrid = () => {
    const rows = []
    for (let row = 0; row < 5; row++) {
      const rowNumbers = []
      for (let col = 0; col < 8; col++) {
        const number = numbers[row * 8 + col]
        rowNumbers.push(
          <NumberButton
            key={number}
            number={number}
            isSelected={isNumberSelected(number)}
            isDisabled={isDisabled}
            onClick={handleNumberClick}
          />
        )
      }
      rows.push(
        <div key={row} className="flex gap-[2px]">
          {rowNumbers}
        </div>
      )
    }
    return rows
  }

  return (
    <>
      <GameMultiplierDisplay multiplier={multiplier} className="top-[23px]" />
      <div className="absolute inset-0 flex">
        <div className="flex-1 flex items-center justify-center">
          <div className="px-[69px] py-[16px]">
            <div className="flex flex-col gap-[2px]">
              {renderNumberGrid()}
            </div>
          </div>
        </div>
        
        <div className="flex flex-col justify-center gap-[2px] pr-[16px]">
          {MULTIPLIER_VALUES.map((value, index) => (
            <MultiplierItem
              key={index}
              value={value}
              isVisible={index < visibleMultipliersCount}
            />
          ))}
        </div>
      </div>
    </>
  )
}
